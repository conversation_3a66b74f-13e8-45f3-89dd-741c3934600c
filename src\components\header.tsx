import { getTranslations } from "next-intl/server";
import { <PERSON> } from "@/i18n/navigation";
import { buttonVariants } from "@/components/ui/button";
import { links } from "@/app/[locale]/links";
import LanguageSelector from "@/app/[locale]/LanguageSelector";
import Logo from "@/assets/imgs/logo.svg";
import { cn } from "@/lib/utils";

const Header = async () => {
  const t = await getTranslations("link");

  return (
    <header className="w-full border-b bg-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="block">
              <Logo className="h-8 w-auto text-black" />
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex items-center space-x-8">
            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-6">
              {Object.entries(links).map(([key, link]) => (
                <Link
                  key={key}
                  href={link.href || "#"}
                  className={cn(
                    buttonVariants({ variant: "link" }),
                    "text-black hover:text-gray-600 p-0 h-auto font-normal"
                  )}
                >
                  {t(link.label.replace("link.", "") as "store" | "contact")}
                </Link>
              ))}
            </div>

            {/* Separator */}
            <div className="hidden md:block h-6 w-px bg-gray-300" />

            {/* Language Selector */}
            <LanguageSelector />

            {/* Separator */}
            <div className="hidden md:block h-6 w-px bg-gray-300" />

            {/* Contact Link (separate from other links for styling) */}
            <Link
              href="#contact"
              className={cn(
                buttonVariants({ variant: "link" }),
                "text-black hover:text-gray-600 p-0 h-auto font-normal"
              )}
            >
              {t("contact")}
            </Link>
          </nav>

          {/* Mobile Menu Button (for future implementation) */}
          <div className="md:hidden">
            {/* Mobile menu button can be added here later */}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
